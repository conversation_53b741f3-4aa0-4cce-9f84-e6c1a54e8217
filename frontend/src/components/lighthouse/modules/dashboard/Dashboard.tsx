import React, { Suspense } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { MetricCard, StatusCard } from '~/components/ui/enhanced-card';
import { LoadingOverlay, ProgressBar, ModuleSkeleton } from '~/components/ui/loading-states';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { cn, typography, layout, animations, states } from '~/lib/ui-utils';
import {
  Brain,
  TrendingUp,
  FileText,
  Bot,
  Lightbulb,
  Clock,
  Target,
  Activity,
  Plus,
  ArrowRight,
} from 'lucide-react';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { QuickActions } from './QuickActions';
import { ActivityFeed } from './ActivityFeed';
import { SmartRecommendations } from './SmartRecommendations';

export function Dashboard() {
  const {
    currentProject,
    projectContext,
    insights,
    activeAgents,
    knowledgeSources,
    learningEvents,
    navigateToModule,
  } = useLighthouseStore();

  if (!currentProject || !projectContext) {
    return (
      <div className={cn(layout.flexColCenter, 'h-full p-6')}>
        <StatusCard
          status="info"
          title="Welcome to Lighthouse"
          description="Create a project to start building contextual intelligence with AI-powered insights and autonomous agents."
          icon={<Brain className="h-5 w-5" />}
          action={
            <Button className={cn(states.hoverButton, animations.scaleOnHover)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Project
            </Button>
          }
          className="max-w-md"
        />
      </div>
    );
  }

  // Calculate metrics
  const recentInsights = insights.filter(
    i => i.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;
  
  const learningVelocity = learningEvents.filter(
    e => e.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000)
  ).length;

  const knowledgeDepth = currentProject.intelligence.domainExpertise.concepts.length;

  return (
    <LoadingOverlay loading={false} className="p-6">
      <div className={cn(layout.spaceY.lg)}>
        {/* Project Overview Metrics */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <MetricCard
            title="Learning Progress"
            value={`${currentProject.intelligence.learningLevel}%`}
            change={{
              value: `+${learningVelocity} today`,
              type: learningVelocity > 0 ? 'increase' : 'neutral'
            }}
            icon={<Brain className="h-4 w-4" />}
            trend={
              <ProgressBar 
                value={currentProject.intelligence.learningLevel}
                size="sm"
                animated
              />
            }
            className={cn(animations.liftOnHover)}
          />

          <MetricCard
            title="Knowledge Depth"
            value={knowledgeDepth}
            subtitle={`${knowledgeSources.length} sources`}
            icon={<FileText className="h-4 w-4" />}
            className={cn(animations.liftOnHover)}
          />

          <MetricCard
            title="Active Intelligence"
            value={activeAgents.length}
            subtitle={`${projectContext.runningAgents.length} in project`}
            icon={<Bot className="h-4 w-4" />}
            className={cn(animations.liftOnHover)}
          />

          <MetricCard
            title="Recent Insights"
            value={recentInsights}
            subtitle={`${insights.length} total`}
            change={{
              value: 'this week',
              type: 'neutral'
            }}
            icon={<Lightbulb className="h-4 w-4" />}
            className={cn(animations.liftOnHover)}
          />
        </div>

        {/* Main Content Grid */}
        <div className={cn(layout.gridResponsive, 'gap-6')}>
          {/* Left Column - Project Status & Quick Actions */}
          <div className={cn(layout.spaceY.lg)}>
            {/* Project Goal Card */}
            <Card className={cn(animations.liftOnHover, 'transition-all duration-200')}>
              <CardHeader>
                <CardTitle className={cn(typography.h4, 'flex items-center gap-2')}>
                  <Target className="h-5 w-5 text-primary" />
                  Project Goal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className={cn(typography.body, 'mb-4')}>{currentProject.goal}</p>
                <div className={cn(layout.spaceY.sm)}>
                  <div className={cn(layout.flexBetween, typography.bodySmall)}>
                    <span className="text-muted-foreground">Status</span>
                    <Badge 
                      variant="outline" 
                      className={cn(
                        'capitalize transition-colors',
                        currentProject.status === 'active' && 'border-green-200 text-green-700 bg-green-50'
                      )}
                    >
                      {currentProject.status}
                    </Badge>
                  </div>
                  <div className={cn(layout.flexBetween, typography.bodySmall)}>
                    <span className="text-muted-foreground">Domain</span>
                    <span className={cn(typography.label, 'capitalize')}>{currentProject.domain}</span>
                  </div>
                  <div className={cn(layout.flexBetween, typography.bodySmall)}>
                    <span className="text-muted-foreground">Created</span>
                    <span className={typography.label}>
                      {new Date(currentProject.created).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Suspense fallback={<div className="h-32 animate-pulse bg-muted rounded-lg" />}>
              <QuickActions />
            </Suspense>
          </div>

          {/* Center Column - Activity Feed */}
          <div>
            <Suspense fallback={<div className="h-96 animate-pulse bg-muted rounded-lg" />}>
              <ActivityFeed />
            </Suspense>
          </div>

          {/* Right Column - AI Recommendations */}
          <div className={cn(layout.spaceY.lg)}>
            <Suspense fallback={<div className="h-64 animate-pulse bg-muted rounded-lg" />}>
              <SmartRecommendations />
            </Suspense>

            {/* Domain Expertise */}
            <Card className={cn(animations.liftOnHover, 'transition-all duration-200')}>
              <CardHeader>
                <CardTitle className={cn(typography.h4, 'flex items-center gap-2')}>
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Domain Expertise
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(layout.spaceY.md)}>
                  <div>
                    <div className={cn(layout.flexBetween, 'mb-2')}>
                      <span className={cn(typography.label)}>
                        {currentProject.intelligence.domainExpertise.primaryDomain}
                      </span>
                      <span className={cn(typography.caption)}>
                        Level {Math.floor(currentProject.intelligence.domainExpertise.expertiseLevel / 20)}
                      </span>
                    </div>
                    <ProgressBar
                      value={currentProject.intelligence.domainExpertise.expertiseLevel}
                      size="sm"
                      variant="success"
                      animated
                    />
                  </div>

                  {currentProject.intelligence.domainExpertise.relatedDomains.length > 0 && (
                    <div>
                      <p className={cn(typography.caption, 'mb-2')}>Related Domains</p>
                      <div className="flex flex-wrap gap-2">
                        {currentProject.intelligence.domainExpertise.relatedDomains.map((domain) => (
                          <Badge 
                            key={domain} 
                            variant="secondary"
                            className={cn(
                              'transition-all duration-150 hover:bg-secondary/80 cursor-pointer',
                              animations.scaleOnHover
                            )}
                          >
                            {domain}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                      'w-full transition-all duration-200',
                      states.hoverButton,
                      states.focusRing,
                      animations.scaleOnHover
                    )}
                    onClick={() => navigateToModule('knowledge')}
                  >
                    Explore Knowledge Graph
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </LoadingOverlay>
  );
}