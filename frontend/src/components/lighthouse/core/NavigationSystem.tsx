import React from 'react';
import { useNavigate } from '@tanstack/react-router';
import {
  LayoutDashboard,
  Library,
  Microscope,
  Bot,
  MessageSquare,
  FileText,
  BarChart3,
  Lightbulb,
} from 'lucide-react';
import { cn, typography, layout, animations, states, accessibility } from '~/lib/ui-utils';
import { ProgressBar } from '~/components/ui/loading-states';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { ModuleName, MODULE_ROUTES } from '../types/navigation.types';

const moduleIcons: Record<ModuleName, React.ElementType> = {
  dashboard: LayoutDashboard,
  knowledge: Library,
  research: Microscope,
  agents: Bot,
  chat: MessageSquare,
  sources: FileText,
  analytics: BarChart3,
  insights: Lightbulb,
};

export function NavigationSystem() {
  const navigate = useNavigate();
  const {
    navigation,
    currentProject,
    setCurrentModule,
    suggestions,
    activeAgents,
    knowledgeSources,
  } = useLighthouseStore();

  const handleModuleClick = (module: ModuleName) => {
    setCurrentModule(module);
    navigate({
      to: '/lighthouse',
      search: { module },
    });
  };

  // Get notification counts for modules
  const getModuleNotifications = (module: ModuleName): number => {
    switch (module) {
      case 'insights':
        return suggestions.filter(s => s.priority === 'high').length;
      case 'agents':
        return activeAgents.length;
      case 'sources':
        return knowledgeSources.filter(s => s.metadata.credibility < 0.5).length;
      default:
        return 0;
    }
  };

  return (
    <aside 
      className="w-64 border-r bg-muted/50 flex flex-col transition-all duration-200"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Module Navigation */}
      <nav className={cn('flex-1 p-4', layout.spaceY.sm)} aria-label="Module navigation">
        {Object.entries(MODULE_ROUTES).map(([key, route]) => {
          const module = key as ModuleName;
          const Icon = moduleIcons[module];
          const isActive = navigation.currentModule === module;
          const notifications = getModuleNotifications(module);
          const isDisabled = !currentProject && module !== 'dashboard';

          return (
            <Button
              key={module}
              variant={isActive ? 'secondary' : 'ghost'}
              className={cn(
                'w-full justify-start relative transition-all duration-200',
                'group hover:bg-accent/50 focus-visible:bg-accent/50',
                isActive && [
                  'bg-secondary shadow-sm border border-border/50',
                  'hover:bg-secondary/80'
                ],
                !isActive && [
                  states.hoverGhost,
                  'hover:translate-x-1 hover:shadow-sm'
                ],
                isDisabled && 'opacity-50 cursor-not-allowed hover:translate-x-0',
                states.focusRing,
                animations.scaleOnHover
              )}
              onClick={() => !isDisabled && handleModuleClick(module)}
              disabled={isDisabled}
              title={isDisabled ? 'Select a project first' : route.description}
              aria-current={isActive ? 'page' : undefined}
              aria-describedby={`${module}-description`}
            >
              <Icon className={cn(
                'h-4 w-4 mr-3 transition-colors duration-200',
                isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground'
              )} />
              <span className={cn(
                typography.label,
                'flex-1 text-left transition-colors duration-200',
                isActive && 'text-foreground font-medium'
              )}>
                {route.label}
              </span>
              {notifications > 0 && (
                <Badge
                  variant={module === 'agents' ? 'default' : 'secondary'}
                  className={cn(
                    'ml-auto h-5 px-1.5 min-w-[20px] transition-all duration-200',
                    'animate-pulse hover:animate-none hover:scale-110'
                  )}
                  aria-label={`${notifications} notifications`}
                >
                  {notifications}
                </Badge>
              )}
              <span 
                id={`${module}-description`} 
                className={accessibility.srOnly}
              >
                {route.description}
              </span>
            </Button>
          );
        })}
      </nav>

      {/* Intelligence Status */}
      {currentProject && (
        <div className="p-4 border-t bg-muted/20">
          <div className={cn(layout.spaceY.sm)}>
            {/* Active Processes */}
            {activeAgents.length > 0 && (
              <div className={cn(layout.flexStart, 'gap-2', typography.bodySmall)}>
                <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse shadow-sm" />
                <span className="text-muted-foreground">
                  {activeAgents.length} process{activeAgents.length !== 1 ? 'es' : ''} running
                </span>
              </div>
            )}

            {/* High Priority Suggestions */}
            {suggestions.filter(s => s.priority === 'high').length > 0 && (
              <div className={cn(layout.flexStart, 'gap-2', typography.bodySmall)}>
                <Lightbulb className="h-3 w-3 text-yellow-500 animate-pulse" />
                <span className="text-muted-foreground">
                  {suggestions.filter(s => s.priority === 'high').length} insights available
                </span>
              </div>
            )}

            {/* Knowledge Growth */}
            {currentProject.intelligence.learningLevel > 0 && (
              <div className="space-y-1">
                <div className={cn(layout.flexBetween, typography.caption)}>
                  <span className="text-muted-foreground">Knowledge Growth</span>
                  <span className={typography.label}>
                    +{Math.round(currentProject.intelligence.learningLevel / 10)}%
                  </span>
                </div>
                <ProgressBar
                  value={currentProject.intelligence.learningLevel}
                  size="sm"
                  variant="success"
                  animated
                  aria-label="Knowledge growth progress"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className={cn('p-4 border-t bg-muted/20', layout.spaceY.sm)}>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'w-full transition-all duration-200',
            states.hoverButton,
            states.focusRing,
            animations.scaleOnHover,
            !currentProject && states.disabled
          )}
          onClick={() => handleModuleClick('chat')}
          disabled={!currentProject}
          aria-label="Start a quick chat conversation"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Quick Question
        </Button>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            'w-full transition-all duration-200',
            states.hoverButton,
            states.focusRing,
            animations.scaleOnHover,
            !currentProject && states.disabled
          )}
          onClick={() => handleModuleClick('sources')}
          disabled={!currentProject}
          aria-label="Add a new source to the project"
        >
          <FileText className="h-4 w-4 mr-2" />
          Add Source
        </Button>
      </div>
    </aside>
  );
}