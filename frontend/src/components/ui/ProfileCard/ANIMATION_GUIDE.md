# ProfileCard Animation Guide

This guide explains the new loading state animations and authentication features added to the ProfileCard component.

## 🎬 Animation Features

### 1. Loading Border Animation
When `authLoading` prop is `true`, the card displays an animated rotating gradient border around the perimeter.

```tsx
<ProfileCard
  isAuthMode={true}
  authLoading={true}
  onLogin={handleLogin}
  // ... other props
/>
```

**Features:**
- Rotating conic gradient with purple/blue colors
- Smooth 2-second rotation cycle
- Non-intrusive - doesn't interfere with form interactions
- Automatically appears when loading state is active

### 2. Error State Border
When `authError` prop contains an error message, the card shows a pulsing red border.

```tsx
<ProfileCard
  isAuthMode={true}
  authError="Invalid credentials"
  onLogin={handleLogin}
  // ... other props
/>
```

**Features:**
- Red gradient border with subtle pulse animation
- 1.5-second pulse cycle
- Clearly indicates authentication failure
- Automatically appears when error is present

### 3. Success Flip Animation
When authentication succeeds, trigger a 3D card flip animation.

```tsx
const profileCardRef = useRef<ProfileCardRef>(null);

const handleLogin = async (email: string, password: string) => {
  // ... authentication logic
  if (success) {
    // Trigger success animation
    profileCardRef.current?.triggerAuthSuccess();
    
    // Transition to profile mode after animation
    setTimeout(() => {
      setIsAuthMode(false);
    }, 800);
  }
};

<ProfileCard
  ref={profileCardRef}
  isAuthMode={true}
  onLogin={handleLogin}
  onAuthSuccess={() => {
    // Optional callback when animation completes
    console.log('Success animation completed');
  }}
  // ... other props
/>
```

**Features:**
- 3D flip animation (0.8 seconds)
- Smooth transition from auth mode to profile mode
- Optional success callback
- Can be triggered programmatically

## 🎯 Usage Examples

### Basic Authentication with Animations
```tsx
import React, { useState, useRef } from 'react';
import ProfileCard, { ProfileCardRef } from './ProfileCard';

const AuthExample = () => {
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState('');
  const [isAuthMode, setIsAuthMode] = useState(true);
  const profileCardRef = useRef<ProfileCardRef>(null);

  const handleLogin = async (email: string, password: string) => {
    setAuthLoading(true);
    setAuthError('');

    try {
      const result = await authenticateUser(email, password);
      
      if (result.success) {
        // Trigger success animation
        profileCardRef.current?.triggerAuthSuccess();
        
        // Switch to profile mode after animation
        setTimeout(() => {
          setIsAuthMode(false);
        }, 800);
      } else {
        setAuthError(result.error);
      }
    } catch (error) {
      setAuthError('Authentication failed');
    } finally {
      setAuthLoading(false);
    }
  };

  return (
    <ProfileCard
      ref={profileCardRef}
      isAuthMode={isAuthMode}
      authLoading={authLoading}
      authError={authError}
      onLogin={handleLogin}
      onAuthSuccess={() => {
        console.log('User authenticated successfully!');
      }}
      // ... other props
    />
  );
};
```

### Testing Animation States
```tsx
// Test loading state
setAuthLoading(true);
setTimeout(() => setAuthLoading(false), 3000);

// Test error state
setAuthError('Demo error message');
setTimeout(() => setAuthError(''), 3000);

// Test success animation
profileCardRef.current?.triggerAuthSuccess();
```

## ♿ Accessibility

The animations respect user accessibility preferences:

```css
@media (prefers-reduced-motion: reduce) {
  /* Reduced motion alternatives */
  .pc-card-wrapper.pc-loading::before {
    animation: loading-border-reduced 3s ease-in-out infinite;
  }
  
  .pc-card-wrapper.pc-flipping {
    animation: success-fade 0.5s ease-in-out;
  }
}
```

**Reduced Motion Features:**
- Loading animation becomes a gentle opacity pulse
- Error animation reduces to subtle opacity changes
- Success animation becomes a fade effect instead of 3D flip
- All animations are slower and less intense

## 🎨 Customization

### Animation Timing
Modify animation durations in CSS:

```css
/* Loading border speed */
.pc-card-wrapper.pc-loading::before {
  animation: loading-border 2s linear infinite; /* Change 2s */
}

/* Error pulse speed */
.pc-card-wrapper.pc-error::before {
  animation: error-pulse 1.5s ease-in-out infinite; /* Change 1.5s */
}

/* Success flip speed */
.pc-card-wrapper.pc-flipping {
  animation: success-flip 0.8s ease-in-out; /* Change 0.8s */
}
```

### Color Themes
Customize animation colors:

```css
/* Loading border colors */
.pc-card-wrapper.pc-loading::before {
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    #your-color-1 90deg,
    #your-color-2 180deg,
    #your-color-1 270deg,
    transparent 360deg
  );
}

/* Error border colors */
.pc-card-wrapper.pc-error::before {
  background: linear-gradient(45deg, #your-error-color, #your-error-color-dark);
}
```

## 🚀 Performance

**Optimizations:**
- Uses CSS transforms and opacity for smooth 60fps animations
- GPU-accelerated with `transform3d` and `will-change`
- Minimal repaints and reflows
- Animations are paused when not visible
- Respects `prefers-reduced-motion` for accessibility

**Best Practices:**
- Don't trigger multiple animations simultaneously
- Allow animations to complete before triggering new ones
- Use the provided timing constants for consistent UX
- Test on lower-end devices for performance

## 📱 Mobile Support

All animations work seamlessly on mobile devices:
- Touch-friendly interactions
- Optimized for mobile performance
- Responsive animation scaling
- Proper touch event handling

## 🔧 Troubleshooting

**Animation not showing:**
- Check that the correct props are set (`authLoading`, `authError`)
- Ensure CSS is properly imported
- Verify browser supports CSS animations

**Performance issues:**
- Check for conflicting CSS animations
- Ensure `will-change` is not overridden
- Test with reduced motion preferences

**Success animation not triggering:**
- Ensure ref is properly attached to ProfileCard
- Call `triggerAuthSuccess()` only when appropriate
- Check timing of state changes
