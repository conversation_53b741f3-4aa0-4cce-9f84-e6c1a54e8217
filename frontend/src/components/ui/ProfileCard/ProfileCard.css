:root {
  --pointer-x: 50%;
  --pointer-y: 50%;
  --pointer-from-center: 0;
  --pointer-from-top: 0.5;
  --pointer-from-left: 0.5;
  --card-opacity: 0;
  --rotate-x: 0deg;
  --rotate-y: 0deg;
  --background-x: 50%;
  --background-y: 50%;
  --grain: none;
  --icon: none;
  --behind-gradient: none;
  --inner-gradient: none;

  --card-radius: 30px;
}

.pc-card-wrapper {
  perspective: 500px;
  transform: translate3d(0, 0, 0.1px);
  position: relative;
  touch-action: none;
}

/* Loading state animations */
.pc-card-wrapper.pc-loading::before {
  content: "";
  position: absolute;
  inset: -3px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    #8b5cf6 90deg,
    #06b6d4 180deg,
    #8b5cf6 270deg,
    transparent 360deg
  );
  border-radius: calc(var(--card-radius) + 3px);
  animation: loading-border 2s linear infinite;
  z-index: -1;
  pointer-events: none;
}

.pc-card-wrapper.pc-loading::after {
  content: "";
  position: absolute;
  inset: -1px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: calc(var(--card-radius) + 1px);
  z-index: -1;
  pointer-events: none;
}

/* Error state styling */
.pc-card-wrapper.pc-error::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #ef4444, #dc2626, #ef4444);
  border-radius: calc(var(--card-radius) + 2px);
  animation: error-pulse 1.5s ease-in-out infinite;
  z-index: -1;
  pointer-events: none;
}

.pc-card-wrapper.pc-error::after {
  content: "";
  position: absolute;
  inset: 0px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: var(--card-radius);
  z-index: -1;
  pointer-events: none;
}

/* Success flip animation */
.pc-card-wrapper.pc-flipping {
  animation: success-flip 0.8s ease-in-out;
}

.pc-card-wrapper.pc-flipping .pc-card {
  animation: card-flip 0.8s ease-in-out;
}

.pc-card-wrapper::before {
  content: "";
  position: absolute;
  inset: -10px;
  background: inherit;
  background-position: inherit;
  border-radius: inherit;
  transition: all 0.5s ease;
  filter: contrast(2) saturate(2) blur(36px);
  transform: scale(0.8) translate3d(0, 0, 0.1px);
  background-size: 100% 100%;
  background-image: var(--behind-gradient);
}

.pc-card-wrapper:hover,
.pc-card-wrapper.active {
  --card-opacity: 1;
}

.pc-card-wrapper:hover::before,
.pc-card-wrapper.active::before {
  filter: contrast(1) saturate(2) blur(40px) opacity(1);
  transform: scale(0.9) translate3d(0, 0, 0.1px);
}

.pc-card {
  height: 80svh;
  max-height: 540px;
  display: grid;
  aspect-ratio: 0.718;
  border-radius: var(--card-radius);
  position: relative;
  background-blend-mode: normal;
  box-shadow: rgba(0, 0, 0, 0.8) calc((var(--pointer-from-left) * 10px) - 3px)
    calc((var(--pointer-from-top) * 20px) - 6px) 20px -5px;
  transition: transform 1s ease;
  transform: translate3d(0, 0, 0.1px) rotateX(0deg) rotateY(0deg);
  background-size: 100% 100%;
  background-position:
    0 0,
    0 0,
    50% 50%,
    0 0;
  background-image:
    radial-gradient(
      farthest-side circle at var(--pointer-x) var(--pointer-y),
      hsla(266, 100%, 90%, var(--card-opacity)) 4%,
      hsla(266, 50%, 80%, calc(var(--card-opacity) * 0.75)) 10%,
      hsla(266, 25%, 70%, calc(var(--card-opacity) * 0.5)) 50%,
      hsla(266, 0%, 60%, 0) 100%
    ),
    linear-gradient(145deg, rgba(96, 73, 110, 0.3) 0%, rgba(113, 196, 255, 0.2) 100%);
  overflow: hidden;
}

.pc-card:hover,
.pc-card.active {
  transition: none;
  transform: translate3d(0, 0, 0.1px) rotateX(var(--rotate-y))
    rotateY(var(--rotate-x));
}

.pc-card * {
  display: grid;
  grid-area: 1/-1;
  border-radius: var(--card-radius);
  transform: translate3d(0, 0, 0.1px);
  pointer-events: none;
}

/* Enable pointer events for authentication form elements */
.pc-auth-content,
.pc-auth-content *,
.pc-form-input,
.pc-auth-submit,
.pc-auth-link,
.pc-password-toggle,
.pc-auth-container,
.pc-auth-form,
.pc-form-group,
.pc-form-label {
  pointer-events: auto !important;
}

/* Override grid layout for auth content */
.pc-auth-content * {
  display: initial !important;
  grid-area: initial !important;
}

/* Ensure form elements are fully interactive */
.pc-auth-content input,
.pc-auth-content button,
.pc-auth-content label {
  pointer-events: auto !important;
  cursor: text;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.pc-auth-content button {
  cursor: pointer !important;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.pc-inside {
  inset: 1px;
  position: absolute;
  background-image: var(--inner-gradient);
  background-color: rgba(0, 0, 0, 0.9);
  transform: translate3d(0, 0, 0.01px);
}

.pc-shine {
  mask-image: var(--icon);
  mask-mode: luminance;
  mask-repeat: repeat;
  mask-size: 150%;
  mask-position: top calc(200% - (var(--background-y) * 5)) left
    calc(100% - var(--background-x));
  transition: filter 0.6s ease;
  filter: brightness(0.66) contrast(1.33) saturate(0.33) opacity(0.5);
  mix-blend-mode: color-dodge;
}

.pc-shine,
.pc-shine::after {
  --space: 5%;
  --angle: -45deg;
  transform: translate3d(0, 0, 1px);
  overflow: hidden;
  z-index: 3;
  background: transparent;
  background-size: cover;
  background-position: center;
  background-image:
    repeating-linear-gradient(
      var(--angle),
      #0e152e 0%,
      hsl(180, 10%, 60%) 3.8%,
      hsl(180, 29%, 66%) 4.5%,
      hsl(180, 10%, 60%) 5.2%,
      #0e152e 10%,
      #0e152e 12%
    ),
    radial-gradient(
      farthest-corner circle at var(--pointer-x) var(--pointer-y),
      hsla(0, 0%, 0%, 0.1) 12%,
      hsla(0, 0%, 0%, 0.15) 20%,
      hsla(0, 0%, 0%, 0.25) 120%
    );
  background-position:
    var(--background-x) var(--background-y),
    center;
  background-blend-mode: hard-light;
  background-size:
    300% 300%,
    200% 200%;
  background-repeat: repeat;
}

.pc-shine::before,
.pc-shine::after {
  content: "";
  background-position: center;
  background-size: cover;
  grid-area: 1/1;
  opacity: 0;
}

.pc-card:hover .pc-shine,
.pc-card.active .pc-shine {
  filter: brightness(0.85) contrast(1.5) saturate(0.5);
}

.pc-card:hover .pc-shine::before,
.pc-card.active .pc-shine::before,
.pc-card:hover .pc-shine::after,
.pc-card.active .pc-shine::after {
  opacity: 1;
}

.pc-shine::before {
  background-image:
    linear-gradient(
      45deg,
      rgba(96, 73, 110, 0.6),
      rgba(113, 196, 255, 0.4),
      rgba(168, 85, 247, 0.5),
      rgba(59, 130, 246, 0.4),
      rgba(96, 73, 110, 0.6)
    ),
    radial-gradient(
      circle at var(--pointer-x) var(--pointer-y),
      hsl(0, 0%, 70%) 0%,
      hsla(0, 0%, 30%, 0.2) 90%
    ),
    var(--grain);
  background-size:
    250% 250%,
    100% 100%,
    220px 220px;
  background-position:
    var(--pointer-x) var(--pointer-y),
    center,
    calc(var(--pointer-x) * 0.01) calc(var(--pointer-y) * 0.01);
  background-blend-mode: color-dodge;
  filter: brightness(calc(2 - var(--pointer-from-center)))
    contrast(calc(var(--pointer-from-center) + 2))
    saturate(calc(0.5 + var(--pointer-from-center)));
  mix-blend-mode: luminosity;
}

.pc-shine::after {
  background-position:
    calc(var(--background-x) * 0.4) calc(var(--background-y) * 0.5),
    center;
  background-size:
    700% 700%,
    100% 100%;
  mix-blend-mode: difference;
  filter: brightness(0.8) contrast(1.5);
}

.pc-glare {
  transform: translate3d(0, 0, 1.1px);
  overflow: hidden;
  background-image: radial-gradient(
    farthest-corner circle at var(--pointer-x) var(--pointer-y),
    hsl(248, 25%, 80%) 12%,
    hsla(207, 40%, 30%, 0.8) 90%
  );
  mix-blend-mode: overlay;
  filter: brightness(0.8) contrast(1.2);
  z-index: 4;
}

.pc-avatar-content {
  mix-blend-mode: screen;
  overflow: hidden;
}

.pc-avatar-content .avatar {
  width: 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%) scale(1);
  bottom: 2px;
  opacity: calc(1.75 - var(--pointer-from-center));
}

.pc-avatar-content::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: 1;
  backdrop-filter: blur(30px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 1) 90%,
    rgba(0, 0, 0, 1) 100%
  );
  pointer-events: none;
}

.pc-user-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 12px 14px;
  pointer-events: auto;
}

.pc-user-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pc-mini-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.pc-mini-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.pc-user-text {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 6px;
}

.pc-handle {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1;
}

.pc-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1;
}

.pc-contact-btn {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.pc-contact-btn:hover {
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.pc-content {
  max-height: 100%;
  overflow: hidden;
  text-align: center;
  position: relative;
  transform: translate3d(
    calc(var(--pointer-from-left) * -6px + 3px),
    calc(var(--pointer-from-top) * -6px + 3px),
    0.1px
  ) !important;
  z-index: 5;
  mix-blend-mode: luminosity;
}

.pc-details {
  width: 100%;
  position: absolute;
  top: 3em;
  display: flex;
  flex-direction: column;
}

.pc-details h3 {
  font-weight: 600;
  margin: 0;
  font-size: min(5svh, 3em);
  margin: 0;
  background-image: linear-gradient(to bottom, #fff, #6f6fbe);
  background-size: 1em 1.5em;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

.pc-details p {
  font-weight: 600;
  position: relative;
  top: -12px;
  white-space: nowrap;
  font-size: 16px;
  margin: 0 auto;
  width: min-content;
  background-image: linear-gradient(to bottom, #fff, #4a4ac0);
  background-size: 1em 1.5em;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}



/* Responsive Design */
@media (max-width: 768px) {
  .pc-card {
    height: 70svh;
    max-height: 450px;
  }

  .pc-details {
    top: 2em;
  }

  .pc-details h3 {
    font-size: min(4svh, 2.5em);
  }

  .pc-details p {
    font-size: 14px;
  }

  .pc-user-info {
    bottom: 15px;
    left: 15px;
    right: 15px;
    padding: 10px 12px;
  }

  .pc-mini-avatar {
    width: 28px;
    height: 28px;
  }

  .pc-user-details {
    gap: 10px;
  }

  .pc-handle {
    font-size: 13px;
  }

  .pc-status {
    font-size: 10px;
  }

  .pc-contact-btn {
    padding: 6px 12px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .pc-card {
    height: 60svh;
    max-height: 380px;
  }

  .pc-details {
    top: 1.5em;
  }

  .pc-details h3 {
    font-size: min(3.5svh, 2em);
  }

  .pc-details p {
    font-size: 12px;
    top: -8px;
  }

  .pc-user-info {
    bottom: 12px;
    left: 12px;
    right: 12px;
    padding: 8px 10px;
    border-radius: 50px;
  }

  .pc-mini-avatar {
    width: 24px;
    height: 24px;
  }

  .pc-user-details {
    gap: 8px;
  }

  .pc-handle {
    font-size: 12px;
  }

  .pc-status {
    font-size: 9px;
  }

  .pc-contact-btn {
    padding: 5px 10px;
    font-size: 10px;
    border-radius: 50px;
  }
}

@media (max-width: 320px) {
  .pc-card {
    height: 55svh;
    max-height: 320px;
  }

  .pc-details h3 {
    font-size: min(3svh, 1.5em);
  }

  .pc-details p {
    font-size: 11px;
  }

  .pc-user-info {
    padding: 6px 8px;
    border-radius: 50px;
  }

  .pc-mini-avatar {
    width: 20px;
    height: 20px;
  }

  .pc-user-details {
    gap: 6px;
  }

  .pc-handle {
    font-size: 11px;
  }

  .pc-status {
    font-size: 8px;
  }

  .pc-contact-btn {
    padding: 4px 8px;
    font-size: 9px;
    border-radius: 50px;
  }
}

/* Authentication UI Styles - Proper Alignment System */
.pc-auth-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
  mix-blend-mode: normal !important;
  box-sizing: border-box;
}

.pc-auth-container {
  width: 100%;
  max-width: 380px;
  z-index: 10;
  position: relative;
  pointer-events: auto;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
  min-height: 0;
}

.pc-auth-header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.pc-auth-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.025em;
  background-image: linear-gradient(135deg, #fff, #a78bfa, #06b6d4);
  background-size: 200% 200%;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
  text-align: center;
  max-width: 100%;
}

.pc-auth-subtitle {
  font-size: 0.9375rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
  text-align: center;
  max-width: 320px;
}

.pc-auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  pointer-events: auto;
  z-index: 10;
  position: relative;
  width: 100%;
  align-items: stretch;
  margin: 0;
  padding: 0;
}

.pc-auth-error {
  padding: 0.875rem 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  color: #fca5a5;
  font-size: 0.875rem;
  text-align: center;
  line-height: 1.4;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
}

.pc-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: auto;
  z-index: 10;
  position: relative;
  width: 100%;
  align-items: stretch;
  margin: 0;
  padding: 0;
}

.pc-form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  pointer-events: auto;
  cursor: pointer;
  line-height: 1.4;
  text-align: left;
  display: block;
  width: 100%;
  padding: 0;
}

.pc-form-input {
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  pointer-events: auto !important;
  cursor: text;
  outline: none;
  z-index: 10;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.pc-form-input:focus {
  outline: none;
  border-color: rgba(168, 85, 247, 0.5);
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.pc-form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.pc-form-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pc-password-input {
  position: relative;
  display: flex;
  align-items: center;
  pointer-events: auto;
  z-index: 10;
  width: 100%;
  margin: 0;
  padding: 0;
}

.pc-password-input input {
  width: 100%;
  pointer-events: auto !important;
  z-index: 11;
  padding-right: 3rem;
  margin: 0;
}

.pc-password-toggle {
  position: absolute;
  right: 0.875rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.5rem;
  font-size: 1rem;
  transition: color 0.2s ease;
  pointer-events: auto !important;
  z-index: 15;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
  margin: 0;
}

.pc-password-toggle:hover {
  color: rgba(255, 255, 255, 0.9);
}

.pc-password-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pc-auth-submit {
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #8b5cf6, #06b6d4);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  margin: 0.75rem 0 0 0;
  pointer-events: auto !important;
  z-index: 10;
  width: 100%;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
}

.pc-auth-submit:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.pc-auth-submit:active:not(:disabled) {
  transform: translateY(0);
}

.pc-auth-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.pc-loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

.pc-auth-footer {
  margin-top: 1.5rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  align-items: center;
  padding: 0;
}

.pc-auth-switch {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
  text-align: center;
  width: 100%;
  padding: 0;
}

.pc-auth-link {
  background: none;
  border: none;
  color: #a78bfa;
  cursor: pointer;
  font-size: inherit;
  font-weight: 500;
  text-decoration: underline;
  transition: color 0.2s ease;
  pointer-events: auto !important;
  z-index: 10;
  position: relative;
}

.pc-auth-link:hover:not(:disabled) {
  color: #c4b5fd;
}

.pc-auth-link:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced hover effects for better interactivity */
.pc-form-input:hover {
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.07);
}

.pc-auth-link:hover:not(:disabled) {
  text-decoration: none;
  transform: translateY(-1px);
}

.pc-auth-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #9333ea, #0891b2);
}

.pc-auth-submit:active:not(:disabled) {
  transform: translateY(1px);
}

/* Improved focus states for accessibility */
.pc-form-input:focus-visible {
  outline: 2px solid rgba(168, 85, 247, 0.6);
  outline-offset: 2px;
}

.pc-auth-submit:focus-visible {
  outline: 2px solid rgba(168, 85, 247, 0.6);
  outline-offset: 2px;
}

.pc-auth-link:focus-visible {
  outline: 2px solid rgba(168, 85, 247, 0.6);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Responsive Authentication Styles */
@media (max-width: 768px) {
  .pc-auth-content {
    padding: 1.5rem;
  }

  .pc-auth-container {
    max-width: 340px;
  }

  .pc-auth-title {
    font-size: 1.625rem;
    line-height: 1.2;
  }

  .pc-auth-subtitle {
    font-size: 0.875rem;
    max-width: 280px;
  }

  .pc-auth-form {
    gap: 1rem;
  }

  .pc-form-input {
    padding: 0.75rem 0.875rem;
    font-size: 0.9375rem;
    min-height: 44px;
  }

  .pc-password-input input {
    padding-right: 2.75rem;
  }

  .pc-password-toggle {
    right: 0.75rem;
    padding: 0.375rem;
  }

  .pc-auth-submit {
    padding: 0.875rem 1.25rem;
    font-size: 0.9375rem;
    min-height: 44px;
  }

  .pc-auth-footer {
    margin-top: 1.25rem;
    gap: 0.875rem;
  }
}

@media (max-width: 480px) {
  .pc-auth-content {
    padding: 1rem;
  }

  .pc-auth-container {
    max-width: 300px;
  }

  .pc-auth-header {
    margin-bottom: 1.5rem;
    gap: 0.625rem;
  }

  .pc-auth-title {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .pc-auth-subtitle {
    font-size: 0.8125rem;
    max-width: 260px;
  }

  .pc-auth-form {
    gap: 1rem;
  }

  .pc-form-input {
    padding: 0.75rem;
    font-size: 0.875rem;
    min-height: 44px;
  }

  .pc-password-input input {
    padding-right: 2.5rem;
  }

  .pc-password-toggle {
    right: 0.625rem;
    padding: 0.25rem;
    font-size: 0.875rem;
  }

  .pc-auth-submit {
    padding: 0.875rem 1rem;
    font-size: 0.875rem;
    min-height: 44px;
    margin-top: 0.5rem;
  }

  .pc-auth-footer {
    margin-top: 1rem;
    gap: 0.75rem;
  }

  .pc-auth-switch {
    font-size: 0.8125rem;
  }
}

@media (max-width: 320px) {
  .pc-auth-content {
    padding: 0.75rem;
  }

  .pc-auth-container {
    max-width: 280px;
  }

  .pc-auth-header {
    margin-bottom: 1.25rem;
    gap: 0.5rem;
  }

  .pc-auth-title {
    font-size: 1.25rem;
    line-height: 1.2;
  }

  .pc-auth-subtitle {
    font-size: 0.75rem;
    max-width: 240px;
  }

  .pc-auth-form {
    gap: 0.875rem;
  }

  .pc-form-label {
    font-size: 0.8125rem;
  }

  .pc-form-input {
    padding: 0.75rem;
    font-size: 0.8125rem;
    min-height: 44px;
  }

  .pc-password-input input {
    padding-right: 2.25rem;
  }

  .pc-password-toggle {
    right: 0.5rem;
    padding: 0.25rem;
    font-size: 0.8125rem;
  }

  .pc-auth-submit {
    padding: 0.875rem;
    font-size: 0.8125rem;
    min-height: 44px;
    margin-top: 0.5rem;
  }

  .pc-auth-footer {
    margin-top: 1rem;
    gap: 0.625rem;
  }

  .pc-auth-switch {
    font-size: 0.75rem;
  }
}

/* Additional Alignment and Spacing Rules */
.pc-auth-content *,
.pc-auth-content *::before,
.pc-auth-content *::after {
  box-sizing: border-box;
}

/* Ensure consistent vertical rhythm */
.pc-auth-container > * + * {
  margin-top: 0;
}

/* Prevent text overflow and ensure proper wrapping */
.pc-auth-title,
.pc-auth-subtitle,
.pc-form-label,
.pc-auth-switch {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Ensure form elements maintain consistent spacing */
.pc-form-group:first-child {
  margin-top: 0;
}

.pc-form-group:last-child {
  margin-bottom: 0;
}

/* Loading spinner alignment */
.pc-loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

/* Animation keyframes */
@keyframes loading-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes error-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes success-flip {
  0% {
    transform: perspective(1000px) rotateY(0deg);
  }
  50% {
    transform: perspective(1000px) rotateY(90deg);
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
  }
}

@keyframes card-flip {
  0% {
    transform: translate3d(0, 0, 0.1px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: translate3d(0, 0, 0.1px) rotateX(0deg) rotateY(90deg);
  }
  100% {
    transform: translate3d(0, 0, 0.1px) rotateX(0deg) rotateY(0deg);
  }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .pc-card-wrapper.pc-loading::before {
    animation: loading-border-reduced 3s ease-in-out infinite;
  }

  .pc-card-wrapper.pc-error::before {
    animation: error-pulse-reduced 2s ease-in-out infinite;
  }

  .pc-card-wrapper.pc-flipping {
    animation: success-fade 0.5s ease-in-out;
  }

  .pc-card-wrapper.pc-flipping .pc-card {
    animation: none;
  }
}

@keyframes loading-border-reduced {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes error-pulse-reduced {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes success-fade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Focus states for better accessibility and alignment */
.pc-form-input:focus-visible,
.pc-auth-submit:focus-visible,
.pc-auth-link:focus-visible,
.pc-password-toggle:focus-visible {
  outline: 2px solid rgba(168, 85, 247, 0.6);
  outline-offset: 2px;
}

/* Ensure buttons maintain consistent height */
.pc-auth-link {
  min-height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  margin: 0;
}
