/**
 * Loading States System - Consistent loading indicators and skeletons
 */

import * as React from 'react';
import { cn } from '~/lib/ui-utils';
import { animations, layout } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader } from './enhanced-card';

// Base loading spinner
interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'accent' | 'muted';
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, size = 'md', variant = 'default', ...props }, ref) => {
    const sizeStyles = {
      sm: 'h-4 w-4 border-2',
      md: 'h-6 w-6 border-2', 
      lg: 'h-8 w-8 border-[3px]',
      xl: 'h-12 w-12 border-4',
    };

    const variantStyles = {
      default: 'border-primary border-r-transparent',
      accent: 'border-accent border-r-transparent',
      muted: 'border-muted-foreground border-r-transparent',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'inline-block rounded-full animate-spin',
          sizeStyles[size],
          variantStyles[variant],
          className
        )}
        role="status"
        aria-label="Loading"
        {...props}
      >
        <span className="sr-only">Loading...</span>
      </div>
    );
  }
);
LoadingSpinner.displayName = 'LoadingSpinner';

// Loading dots animation
interface LoadingDotsProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
}

const LoadingDots = React.forwardRef<HTMLDivElement, LoadingDotsProps>(
  ({ className, size = 'md', ...props }, ref) => {
    const sizeStyles = {
      sm: 'w-1 h-1',
      md: 'w-2 h-2',
      lg: 'w-3 h-3',
    };

    return (
      <div
        ref={ref}
        className={cn('flex space-x-1', className)}
        role="status"
        aria-label="Loading"
        {...props}
      >
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              'rounded-full bg-current animate-pulse',
              sizeStyles[size]
            )}
            style={{
              animationDelay: `${i * 0.15}s`,
              animationDuration: '0.8s',
            }}
          />
        ))}
        <span className="sr-only">Loading...</span>
      </div>
    );
  }
);
LoadingDots.displayName = 'LoadingDots';

// Skeleton components for content loading
interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'rectangular' | 'circular' | 'text';
  animation?: 'pulse' | 'wave' | 'none';
}

const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant = 'rectangular', animation = 'pulse', ...props }, ref) => {
    const variantStyles = {
      rectangular: 'rounded-md',
      circular: 'rounded-full',
      text: 'rounded-sm',
    };

    const animationStyles = {
      pulse: 'animate-pulse',
      wave: 'animate-pulse', // Could be enhanced with wave animation
      none: '',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'bg-muted-foreground/20',
          variantStyles[variant],
          animationStyles[animation],
          className
        )}
        aria-hidden="true"
        {...props}
      />
    );
  }
);
Skeleton.displayName = 'Skeleton';

// Pre-built skeleton patterns
const SkeletonCard = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <Card ref={ref} className={cn('w-full', className)} {...props}>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Skeleton variant="circular" className="h-10 w-10" />
          <div className="space-y-2 flex-1">
            <Skeleton variant="text" className="h-4 w-1/3" />
            <Skeleton variant="text" className="h-3 w-1/2" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <Skeleton variant="text" className="h-4 w-full" />
          <Skeleton variant="text" className="h-4 w-3/4" />
          <Skeleton variant="text" className="h-4 w-1/2" />
        </div>
      </CardContent>
    </Card>
  )
);
SkeletonCard.displayName = 'SkeletonCard';

const SkeletonList = React.forwardRef<HTMLDivElement, { items?: number } & React.HTMLAttributes<HTMLDivElement>>(
  ({ className, items = 3, ...props }, ref) => (
    <div ref={ref} className={cn('space-y-3', className)} {...props}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3">
          <Skeleton variant="circular" className="h-8 w-8" />
          <div className="space-y-2 flex-1">
            <Skeleton variant="text" className="h-3 w-1/4" />
            <Skeleton variant="text" className="h-4 w-3/4" />
          </div>
        </div>
      ))}
    </div>
  )
);
SkeletonList.displayName = 'SkeletonList';

const SkeletonTable = React.forwardRef<HTMLDivElement, { rows?: number; cols?: number } & React.HTMLAttributes<HTMLDivElement>>(
  ({ className, rows = 5, cols = 4, ...props }, ref) => (
    <div ref={ref} className={cn('space-y-3', className)} {...props}>
      {/* Table header */}
      <div className="flex space-x-4">
        {Array.from({ length: cols }).map((_, i) => (
          <Skeleton key={i} variant="text" className="h-4 flex-1" />
        ))}
      </div>
      
      {/* Table rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: cols }).map((_, colIndex) => (
            <Skeleton key={colIndex} variant="text" className="h-6 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
);
SkeletonTable.displayName = 'SkeletonTable';

// Loading overlay for components
interface LoadingOverlayProps extends React.HTMLAttributes<HTMLDivElement> {
  loading: boolean;
  children: React.ReactNode;
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  backdrop?: 'blur' | 'solid' | 'transparent';
}

const LoadingOverlay = React.forwardRef<HTMLDivElement, LoadingOverlayProps>(
  ({ 
    loading, 
    children, 
    spinnerSize = 'lg', 
    message = 'Loading...', 
    backdrop = 'blur',
    className,
    ...props 
  }, ref) => {
    const backdropStyles = {
      blur: 'backdrop-blur-sm bg-background/80',
      solid: 'bg-background/95',
      transparent: 'bg-transparent',
    };

    return (
      <div ref={ref} className={cn('relative', className)} {...props}>
        {children}
        
        {loading && (
          <div 
            className={cn(
              'absolute inset-0 z-50 flex items-center justify-center',
              backdropStyles[backdrop],
              animations.fadeIn
            )}
            role="status"
            aria-live="polite"
            aria-label={message}
          >
            <div className="text-center space-y-4">
              <LoadingSpinner size={spinnerSize} />
              {message && (
                <p className="text-sm text-muted-foreground">{message}</p>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);
LoadingOverlay.displayName = 'LoadingOverlay';

// Progress bar with enhanced features
interface ProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
  animated?: boolean;
  showValue?: boolean;
  label?: string;
}

const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ 
    value, 
    max = 100, 
    size = 'md', 
    variant = 'default',
    animated = false,
    showValue = false,
    label,
    className,
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
    
    const sizeStyles = {
      sm: 'h-2',
      md: 'h-3',
      lg: 'h-4',
    };

    const variantStyles = {
      default: 'bg-primary',
      success: 'bg-green-500',
      warning: 'bg-yellow-500', 
      error: 'bg-red-500',
    };

    return (
      <div ref={ref} className={cn('space-y-2', className)} {...props}>
        {(label || showValue) && (
          <div className="flex justify-between text-sm">
            {label && <span className="text-muted-foreground">{label}</span>}
            {showValue && <span className="font-medium">{Math.round(percentage)}%</span>}
          </div>
        )}
        
        <div 
          className={cn(
            'w-full bg-muted rounded-full overflow-hidden',
            sizeStyles[size]
          )}
          role="progressbar"
          aria-valuenow={value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-label={label}
        >
          <div
            className={cn(
              'h-full transition-all duration-300 ease-out rounded-full',
              variantStyles[variant],
              animated && 'animate-pulse'
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
      </div>
    );
  }
);
ProgressBar.displayName = 'ProgressBar';

// Module skeleton for Lighthouse modules
const ModuleSkeleton = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex h-full', className)} {...props}>
      {/* Sidebar skeleton */}
      <div className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-6 w-32" />
          <div className="grid grid-cols-2 gap-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="p-3 border rounded">
                <Skeleton className="h-8 w-12 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          <Skeleton className="h-5 w-24" />
          <SkeletonList items={3} />
        </div>
      </div>
      
      {/* Main content skeleton */}
      <div className="flex-1 p-6 space-y-6">
        <div className="flex items-center space-x-4 mb-6">
          <Skeleton className="h-8 w-64" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    </div>
  )
);
ModuleSkeleton.displayName = 'ModuleSkeleton';

export {
  LoadingSpinner,
  LoadingDots,
  Skeleton,
  SkeletonCard,
  SkeletonList,
  SkeletonTable,
  LoadingOverlay,
  ProgressBar,
  ModuleSkeleton,
};