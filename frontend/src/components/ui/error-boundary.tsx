/**
 * Enhanced Error Boundary System with Accessibility and Recovery
 */

import React from 'react';
import { cn, typography, layout, animations } from '~/lib/ui-utils';
import { StatusCard } from './enhanced-card';
import { Button } from './button';
import { RefreshCw, AlertTriangle, Home, ArrowLeft, Bug } from 'lucide-react';

// Error boundary state interface
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

// Error boundary props
interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
  showDetails?: boolean;
}

// Error fallback component props
interface ErrorFallbackProps {
  error: Error;
  errorInfo: React.ErrorInfo;
  resetError: () => void;
  level: 'page' | 'section' | 'component';
  showDetails: boolean;
  errorId: string;
}

// Default error fallback component
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  resetError,
  level,
  showDetails,
  errorId
}) => {
  const [detailsExpanded, setDetailsExpanded] = React.useState(false);
  const [reportSent, setReportSent] = React.useState(false);

  const handleSendReport = async () => {
    try {
      // In a real app, this would send to your error reporting service
      console.error('Error Report:', {
        errorId,
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      });
      setReportSent(true);
    } catch (e) {
      console.error('Failed to send error report:', e);
    }
  };

  const getErrorTitle = () => {
    switch (level) {
      case 'page':
        return 'Page Error';
      case 'section':
        return 'Section Error';
      case 'component':
        return 'Component Error';
      default:
        return 'Something went wrong';
    }
  };

  const getErrorDescription = () => {
    switch (level) {
      case 'page':
        return 'This page encountered an error and couldn\'t be displayed properly. You can try refreshing the page or navigate back to the previous page.';
      case 'section':
        return 'This section encountered an error. Other parts of the page should still work normally. You can try refreshing to resolve the issue.';
      case 'component':
        return 'This component encountered an error but the rest of the interface should work normally.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  };

  return (
    <div
      className={cn(
        level === 'page' ? layout.flexColCenter + ' min-h-screen p-6' : 'p-4',
        animations.fadeIn
      )}
      role="alert"
      aria-live="assertive"
    >
      <StatusCard
        status="error"
        title={getErrorTitle()}
        description={getErrorDescription()}
        icon={<AlertTriangle className="h-5 w-5" />}
        action={
          <div className={cn(layout.spaceY.sm, 'w-full')}>
            {/* Primary actions */}
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={resetError}
                className={cn(animations.scaleOnHover)}
                aria-label="Try again"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              
              {level === 'page' && (
                <>
                  <Button
                    variant="outline"
                    onClick={() => window.history.back()}
                    className={cn(animations.scaleOnHover)}
                    aria-label="Go back to previous page"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go Back
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/'}
                    className={cn(animations.scaleOnHover)}
                    aria-label="Return to homepage"
                  >
                    <Home className="h-4 w-4 mr-2" />
                    Home
                  </Button>
                </>
              )}
            </div>

            {/* Secondary actions */}
            {showDetails && (
              <div className="border-t pt-4 space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDetailsExpanded(!detailsExpanded)}
                  className="text-xs"
                  aria-expanded={detailsExpanded}
                  aria-controls="error-details"
                >
                  {detailsExpanded ? 'Hide' : 'Show'} Technical Details
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSendReport}
                  disabled={reportSent}
                  className="text-xs"
                  aria-label={reportSent ? 'Report sent' : 'Send error report'}
                >
                  <Bug className="h-3 w-3 mr-1" />
                  {reportSent ? 'Report Sent' : 'Report Issue'}
                </Button>
              </div>
            )}

            {/* Error details */}
            {showDetails && detailsExpanded && (
              <div
                id="error-details"
                className={cn(
                  'mt-4 p-3 bg-muted rounded-md text-xs space-y-2',
                  animations.slideInUp
                )}
                role="region"
                aria-label="Error details"
              >
                <div>
                  <strong className="text-foreground">Error ID:</strong>
                  <code className="ml-2 font-mono text-muted-foreground">
                    {errorId}
                  </code>
                </div>
                
                <div>
                  <strong className="text-foreground">Message:</strong>
                  <code className="ml-2 font-mono text-muted-foreground break-all">
                    {error.message}
                  </code>
                </div>
                
                {error.stack && (
                  <div>
                    <strong className="text-foreground">Stack Trace:</strong>
                    <pre className="mt-1 p-2 bg-background rounded text-xs overflow-auto max-h-32 border">
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        }
        className={cn('max-w-2xl', level === 'component' && 'max-w-md')}
      />
    </div>
  );
};

// Enhanced Error Boundary Class Component
class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Generate unique error ID
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo });
    
    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  render() {
    if (this.state.hasError && this.state.error && this.state.errorInfo) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          resetError={this.resetError}
          level={this.props.level || 'component'}
          showDetails={this.props.showDetails ?? process.env.NODE_ENV === 'development'}
          errorId={this.state.errorId}
        />
      );
    }

    return this.props.children;
  }
}

// Hook for functional components error handling
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const throwError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { throwError, resetError };
}

// Higher-order component for adding error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Specific error boundary variants for common use cases
export const PageErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <ErrorBoundary {...props} level="page" />
);

export const SectionErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <ErrorBoundary {...props} level="section" />
);

export const ComponentErrorBoundary: React.FC<Omit<ErrorBoundaryProps, 'level'>> = (props) => (
  <ErrorBoundary {...props} level="component" />
);

// Error boundary for async operations
interface AsyncErrorBoundaryProps extends ErrorBoundaryProps {
  onRetry?: () => void | Promise<void>;
}

export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  onRetry,
  ...props
}) => {
  const fallback = React.useCallback(
    (fallbackProps: ErrorFallbackProps) => {
      const handleRetry = async () => {
        fallbackProps.resetError();
        if (onRetry) {
          try {
            await onRetry();
          } catch (error) {
            // Error will be caught by the boundary
          }
        }
      };

      return (
        <DefaultErrorFallback
          {...fallbackProps}
          resetError={handleRetry}
        />
      );
    },
    [onRetry]
  );

  return <ErrorBoundary {...props} fallback={fallback} />;
};

export {
  ErrorBoundary,
  DefaultErrorFallback,
  type ErrorBoundaryProps,
  type ErrorFallbackProps,
};