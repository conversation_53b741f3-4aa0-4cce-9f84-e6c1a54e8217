/**
 * Responsive Layout System for Lighthouse - Mobile-first design
 */

import React, { useState, useEffect } from 'react';
import { cn, layout, animations, accessibility } from '~/lib/ui-utils';
import { Sheet, SheetContent, SheetTrigger } from './sheet';
import { But<PERSON> } from './button';
import { ScrollArea } from './scroll-area';
import { Menu, X, ChevronLeft, ChevronRight } from 'lucide-react';

// Breakpoint hook for responsive behavior
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop' | 'wide'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else if (width < 1440) {
        setBreakpoint('desktop');
      } else {
        setBreakpoint('wide');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}

// Mobile-first container component
interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  center?: boolean;
}

export const ResponsiveContainer = React.forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ className, maxWidth = 'full', padding = 'md', center = true, children, ...props }, ref) => {
    const maxWidthClasses = {
      sm: 'max-w-sm',
      md: 'max-w-md', 
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      full: 'max-w-full',
    };

    const paddingClasses = {
      none: '',
      sm: 'px-4 py-2',
      md: 'px-4 sm:px-6 py-4',
      lg: 'px-4 sm:px-6 lg:px-8 py-6',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'w-full',
          maxWidthClasses[maxWidth],
          paddingClasses[padding],
          center && 'mx-auto',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
ResponsiveContainer.displayName = 'ResponsiveContainer';

// Responsive grid system
interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
}

export const ResponsiveGrid = React.forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ 
    className, 
    cols = { default: 1, md: 2, lg: 3 }, 
    gap = 'md',
    align = 'stretch',
    children, 
    ...props 
  }, ref) => {
    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };

    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
    };

    const gridCols = cn(
      'grid',
      cols.default && `grid-cols-${cols.default}`,
      cols.sm && `sm:grid-cols-${cols.sm}`,
      cols.md && `md:grid-cols-${cols.md}`,
      cols.lg && `lg:grid-cols-${cols.lg}`,
      cols.xl && `xl:grid-cols-${cols.xl}`,
      gapClasses[gap],
      alignClasses[align]
    );

    return (
      <div
        ref={ref}
        className={cn(gridCols, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
ResponsiveGrid.displayName = 'ResponsiveGrid';

// Mobile-friendly sidebar component
interface ResponsiveSidebarProps {
  children: React.ReactNode;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  side?: 'left' | 'right';
  width?: string;
  title?: string;
}

export const ResponsiveSidebar: React.FC<ResponsiveSidebarProps> = ({
  children,
  trigger,
  open,
  onOpenChange,
  side = 'left',
  width = '320px',
  title = 'Navigation'
}) => {
  const breakpoint = useBreakpoint();
  const [internalOpen, setInternalOpen] = useState(false);
  
  const isOpen = open !== undefined ? open : internalOpen;
  const setIsOpen = onOpenChange || setInternalOpen;

  // On mobile/tablet, use sheet overlay
  if (breakpoint === 'mobile' || breakpoint === 'tablet') {
    return (
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          {trigger || (
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'fixed top-4 z-40 transition-all duration-200',
                side === 'left' ? 'left-4' : 'right-4',
                animations.scaleOnHover
              )}
              aria-label={`Open ${title.toLowerCase()}`}
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}
        </SheetTrigger>
        
        <SheetContent 
          side={side}
          className={cn('p-0', `w-[${width}]`)}
          aria-label={title}
        >
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">{title}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                aria-label="Close navigation"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <ScrollArea className="flex-1">
              <div className="p-4">
                {children}
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // On desktop, show fixed sidebar
  return (
    <aside
      className={cn(
        'fixed top-0 bottom-0 z-30 flex flex-col border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200',
        side === 'left' ? 'left-0' : 'right-0',
        `w-[${width}]`
      )}
      aria-label={title}
    >
      {children}
    </aside>
  );
};

// Collapsible panel for desktop responsive behavior
interface CollapsiblePanelProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string;
  defaultExpanded?: boolean;
  expandedWidth?: string;
  collapsedWidth?: string;
  side?: 'left' | 'right';
}

export const CollapsiblePanel = React.forwardRef<HTMLDivElement, CollapsiblePanelProps>(
  ({ 
    className,
    title,
    defaultExpanded = true,
    expandedWidth = '320px',
    collapsedWidth = '60px',
    side = 'left',
    children,
    ...props 
  }, ref) => {
    const [expanded, setExpanded] = useState(defaultExpanded);
    const breakpoint = useBreakpoint();

    // Auto-collapse on smaller screens
    useEffect(() => {
      if (breakpoint === 'tablet') {
        setExpanded(false);
      } else if (breakpoint === 'desktop' || breakpoint === 'wide') {
        setExpanded(defaultExpanded);
      }
    }, [breakpoint, defaultExpanded]);

    const toggleExpanded = () => {
      setExpanded(!expanded);
    };

    return (
      <div
        ref={ref}
        className={cn(
          'relative border-r bg-muted/50 transition-all duration-300 overflow-hidden',
          className
        )}
        style={{
          width: expanded ? expandedWidth : collapsedWidth,
        }}
        {...props}
      >
        {/* Toggle button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleExpanded}
          className={cn(
            'absolute top-4 z-10 transition-all duration-200',
            side === 'left' ? 'right-2' : 'left-2',
            animations.scaleOnHover
          )}
          aria-label={expanded ? `Collapse ${title}` : `Expand ${title}`}
          aria-expanded={expanded}
        >
          {side === 'left' ? (
            expanded ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
          ) : (
            expanded ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />
          )}
        </Button>

        {/* Content */}
        <div className={cn(
          'flex flex-col h-full transition-opacity duration-200',
          expanded ? 'opacity-100' : 'opacity-0 pointer-events-none'
        )}>
          {children}
        </div>

        {/* Collapsed state indicator */}
        {!expanded && (
          <div 
            className="flex flex-col items-center justify-center h-full p-2"
            aria-hidden="true"
          >
            <div className="writing-vertical text-xs text-muted-foreground transform -rotate-90 whitespace-nowrap">
              {title}
            </div>
          </div>
        )}
      </div>
    );
  }
);
CollapsiblePanel.displayName = 'CollapsiblePanel';

// Responsive stack component (changes from horizontal to vertical on mobile)
interface ResponsiveStackProps extends React.HTMLAttributes<HTMLDivElement> {
  direction?: {
    default?: 'row' | 'column';
    sm?: 'row' | 'column';
    md?: 'row' | 'column';
    lg?: 'row' | 'column';
  };
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  wrap?: boolean;
}

export const ResponsiveStack = React.forwardRef<HTMLDivElement, ResponsiveStackProps>(
  ({ 
    className,
    direction = { default: 'column', md: 'row' },
    align = 'stretch',
    justify = 'start',
    gap = 'md',
    wrap = false,
    children,
    ...props 
  }, ref) => {
    const gapClasses = {
      none: 'gap-0',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };

    const alignClasses = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
    };

    const justifyClasses = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    };

    const directionClasses = cn(
      'flex',
      direction.default === 'row' ? 'flex-row' : 'flex-col',
      direction.sm && (direction.sm === 'row' ? 'sm:flex-row' : 'sm:flex-col'),
      direction.md && (direction.md === 'row' ? 'md:flex-row' : 'md:flex-col'),
      direction.lg && (direction.lg === 'row' ? 'lg:flex-row' : 'lg:flex-col'),
      wrap && 'flex-wrap',
      gapClasses[gap],
      alignClasses[align],
      justifyClasses[justify]
    );

    return (
      <div
        ref={ref}
        className={cn(directionClasses, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
ResponsiveStack.displayName = 'ResponsiveStack';

// Utility component for hiding/showing elements at breakpoints
interface ResponsiveShowProps extends React.HTMLAttributes<HTMLDivElement> {
  above?: 'sm' | 'md' | 'lg' | 'xl';
  below?: 'sm' | 'md' | 'lg' | 'xl';
  only?: 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveShow = React.forwardRef<HTMLDivElement, ResponsiveShowProps>(
  ({ className, above, below, only, children, ...props }, ref) => {
    const showClasses = cn(
      above && {
        sm: 'hidden sm:block',
        md: 'hidden md:block', 
        lg: 'hidden lg:block',
        xl: 'hidden xl:block',
      }[above],
      
      below && {
        sm: 'block sm:hidden',
        md: 'block md:hidden',
        lg: 'block lg:hidden', 
        xl: 'block xl:hidden',
      }[below],
      
      only && {
        sm: 'hidden sm:block md:hidden',
        md: 'hidden md:block lg:hidden',
        lg: 'hidden lg:block xl:hidden',
        xl: 'hidden xl:block',
      }[only]
    );

    return (
      <div
        ref={ref}
        className={cn(showClasses, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
ResponsiveShow.displayName = 'ResponsiveShow';

// Adaptive layout component that changes layout based on screen size
interface AdaptiveLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  mobile?: React.ReactNode;
  tablet?: React.ReactNode;
  desktop?: React.ReactNode;
  wide?: React.ReactNode;
}

export const AdaptiveLayout: React.FC<AdaptiveLayoutProps> = ({
  mobile,
  tablet,
  desktop,
  wide,
  children,
  className,
  ...props
}) => {
  const breakpoint = useBreakpoint();

  let content = children;
  
  if (breakpoint === 'mobile' && mobile) {
    content = mobile;
  } else if (breakpoint === 'tablet' && tablet) {
    content = tablet;
  } else if (breakpoint === 'desktop' && desktop) {
    content = desktop;
  } else if (breakpoint === 'wide' && wide) {
    content = wide;
  }

  return (
    <div className={cn(animations.fadeIn, className)} {...props}>
      {content}
    </div>
  );
};

// useBreakpoint is already exported above