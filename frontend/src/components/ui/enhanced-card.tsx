/**
 * Enhanced Card Component with improved accessibility and microinteractions
 */

import * as React from 'react';
import { cn } from '~/lib/ui-utils';
import { typography, layout, animations, states } from '~/lib/ui-utils';

// Base card interfaces
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  interactive?: boolean;
  loading?: boolean;
}

interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  asChild?: boolean;
}

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  asChild?: boolean;
}

interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: 'left' | 'center' | 'right' | 'between';
}

// Card variants
const cardVariants = {
  default: 'bg-card text-card-foreground border border-border',
  elevated: 'bg-card text-card-foreground shadow-md border-0',
  outlined: 'bg-transparent border-2 border-border',
  filled: 'bg-muted text-muted-foreground border-0',
} as const;

// Enhanced Card component
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', interactive = false, loading = false, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          'rounded-lg transition-all duration-200',
          
          // Variant styles
          cardVariants[variant],
          
          // Interactive styles
          interactive && [
            'cursor-pointer',
            states.hoverCard,
            states.focusRing,
            states.activeButton,
            'hover:shadow-lg hover:scale-[1.02]',
          ],
          
          // Loading state
          loading && 'animate-pulse pointer-events-none',
          
          className
        )}
        role={interactive ? 'button' : undefined}
        tabIndex={interactive ? 0 : undefined}
        aria-busy={loading}
        {...props}
      >
        {loading ? (
          <div className="p-6 space-y-3">
            <div className="h-4 bg-muted-foreground/20 rounded animate-pulse" />
            <div className="h-3 bg-muted-foreground/20 rounded w-2/3 animate-pulse" />
            <div className="h-3 bg-muted-foreground/20 rounded w-1/2 animate-pulse" />
          </div>
        ) : (
          children
        )}
      </div>
    );
  }
);
Card.displayName = 'Card';

// Enhanced Card Header
const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}
      {...props}
    >
      {children}
    </div>
  )
);
CardHeader.displayName = 'CardHeader';

// Enhanced Card Title with proper heading levels
const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, level = 3, children, ...props }, ref) => {
    const HeadingComponent = `h${level}` as keyof JSX.IntrinsicElements;
    
    const headingStyles = {
      1: typography.h1,
      2: typography.h2, 
      3: typography.h3,
      4: typography.h4,
      5: typography.h5,
      6: typography.h6,
    };

    return React.createElement(
      HeadingComponent,
      {
        ref,
        className: cn(headingStyles[level], 'leading-none tracking-tight', className),
        ...props,
      },
      children
    );
  }
);
CardTitle.displayName = 'CardTitle';

// Enhanced Card Description
const CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn(typography.caption, 'leading-relaxed', className)}
      {...props}
    />
  )
);
CardDescription.displayName = 'CardDescription';

// Enhanced Card Content with spacing options
const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, spacing = 'md', children, ...props }, ref) => {
    const spacingStyles = {
      none: 'p-0',
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8',
    };

    return (
      <div
        ref={ref}
        className={cn(spacingStyles[spacing], 'pt-0', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CardContent.displayName = 'CardContent';

// Enhanced Card Footer with alignment options
const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, align = 'left', children, ...props }, ref) => {
    const alignmentStyles = {
      left: 'justify-start',
      center: 'justify-center', 
      right: 'justify-end',
      between: 'justify-between',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center p-6 pt-0',
          alignmentStyles[align],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CardFooter.displayName = 'CardFooter';

// Metric Card - Specialized card for displaying metrics
interface MetricCardProps extends Omit<CardProps, 'children'> {
  title: string;
  value: string | number;
  change?: {
    value: string | number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: React.ReactNode;
  subtitle?: string;
  trend?: React.ReactNode;
}

const MetricCard = React.forwardRef<HTMLDivElement, MetricCardProps>(
  ({ title, value, change, icon, subtitle, trend, className, ...props }, ref) => {
    const changeColors = {
      increase: 'text-green-600 dark:text-green-400',
      decrease: 'text-red-600 dark:text-red-400', 
      neutral: 'text-muted-foreground',
    };

    return (
      <Card ref={ref} variant="elevated" className={className} {...props}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle level={6} className="text-sm font-medium text-muted-foreground">
              {title}
            </CardTitle>
            {icon && (
              <div className="text-muted-foreground" aria-hidden="true">
                {icon}
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent spacing="sm" className="pt-0">
          <div className="space-y-1">
            <div className={cn(typography.h2, 'font-bold')}>
              {value}
            </div>
            
            {(change || subtitle) && (
              <div className="flex items-center justify-between text-xs">
                {change && (
                  <span className={changeColors[change.type]}>
                    {change.type === 'increase' ? '+' : change.type === 'decrease' ? '-' : ''}
                    {change.value}
                  </span>
                )}
                {subtitle && (
                  <span className="text-muted-foreground">{subtitle}</span>
                )}
              </div>
            )}
            
            {trend && (
              <div className="mt-2">
                {trend}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }
);
MetricCard.displayName = 'MetricCard';

// Status Card - For displaying status information with appropriate colors
interface StatusCardProps extends Omit<CardProps, 'children'> {
  status: 'success' | 'warning' | 'error' | 'info';
  title: string;
  description?: string;
  action?: React.ReactNode;
  icon?: React.ReactNode;
}

const StatusCard = React.forwardRef<HTMLDivElement, StatusCardProps>(
  ({ status, title, description, action, icon, className, ...props }, ref) => {
    const statusStyles = {
      success: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950',
      warning: 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950',
      error: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950',
      info: 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950',
    };

    const iconColors = {
      success: 'text-green-500',
      warning: 'text-yellow-500', 
      error: 'text-red-500',
      info: 'text-blue-500',
    };

    return (
      <Card 
        ref={ref}
        variant="outlined"
        className={cn(statusStyles[status], className)}
        {...props}
      >
        <CardContent>
          <div className="flex items-start space-x-3">
            {icon && (
              <div className={cn('mt-0.5', iconColors[status])} aria-hidden="true">
                {icon}
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <CardTitle level={4} className="text-sm font-medium">
                {title}
              </CardTitle>
              
              {description && (
                <CardDescription className="mt-1">
                  {description}
                </CardDescription>
              )}
              
              {action && (
                <div className="mt-3">
                  {action}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);
StatusCard.displayName = 'StatusCard';

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  MetricCard,
  StatusCard,
};