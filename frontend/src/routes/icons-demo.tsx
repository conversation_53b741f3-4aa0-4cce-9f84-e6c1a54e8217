import { createFileRoute } from '@tanstack/react-router'
import { Icons } from '~/components/ui/icons'

export const Route = createFileRoute('/icons-demo')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">Icons Demo</h1>
      <div className="grid grid-cols-6 gap-4">
        <div className="flex flex-col items-center p-4 border rounded-lg">
          <Icons.search className="h-8 w-8 mb-2" />
          <span className="text-sm">Search</span>
        </div>
        <div className="flex flex-col items-center p-4 border rounded-lg">
          <Icons.bell className="h-8 w-8 mb-2" />
          <span className="text-sm">Bell</span>
        </div>
        <div className="flex flex-col items-center p-4 border rounded-lg">
          <Icons.home className="h-8 w-8 mb-2" />
          <span className="text-sm">Home</span>
        </div>
        <div className="flex flex-col items-center p-4 border rounded-lg">
          <Icons.check className="h-8 w-8 mb-2" />
          <span className="text-sm">Check</span>
        </div>
        <div className="flex flex-col items-center p-4 border rounded-lg">
          <Icons.settings className="h-8 w-8 mb-2" />
          <span className="text-sm">Settings</span>
        </div>
      </div>
    </div>
  )
}