import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { motion, AnimatePresence } from 'framer-motion'
import * as React from 'react'
import { useEffect, useRef, useCallback } from 'react'
import { ChartPieIcon } from '@/components/ui/icons/chart-pie'
import { CogIcon } from '@/components/ui/icons/cog'
import { WorkflowIcon } from '@/components/ui/icons/workflow'
import { UploadIcon } from '@/components/ui/icons/upload'
import { AudioLinesIcon } from '@/components/ui/icons/audio-lines'
import { ArrowRightIcon } from '@/components/ui/icons/arrow-right'


export const Route = createFileRoute('/')({
  component: HomeComponent,
})

function HomeComponent() {
  // Get user's name (you can replace this with actual user data from your auth system)
  const userName = "Ahmed" // This should come from your authentication context
  
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950 relative">
      {/* Subtle geometric pattern */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_35%,rgba(59,130,246,0.1)_35%,rgba(59,130,246,0.1)_65%,transparent_65%),linear-gradient(-45deg,transparent_35%,rgba(147,51,234,0.1)_35%,rgba(147,51,234,0.1)_65%,transparent_65%)] bg-[length:60px_60px]" />
      </div>
      
      {/* Hero Section */}
      <div className="relative z-10 pt-12 pb-6">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-6"
          >
            <h1 className="text-4xl md:text-5xl font-light text-gray-900 dark:text-white mb-2 tracking-wide">
              Welcome back, <span className="font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{userName}</span>
            </h1>
            <div className="w-24 h-px bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-4" />
          </motion.div>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto font-light leading-relaxed"
          >
            Your intelligent business companion is ready to assist
          </motion.p>
        </div>
      </div>
      
      <div className="relative z-10 px-4 pb-16">
        <ChatInterface />
      </div>
    </div>
  )
}

interface Message {
  id: string
  text: string
  isUser: boolean
  timestamp: Date
  type?: 'success' | 'warning' | 'info' | 'error'
}

interface SuggestionCategory {
  name: string
  icon: React.ComponentType<{ size?: number; className?: string }>
  suggestions: string[]
}

function ChatInterface() {
  const [message, setMessage] = React.useState('')
  const [messages, setMessages] = React.useState<Message[]>([])
  const [isTyping, setIsTyping] = React.useState(false)
  const [isFocused, setIsFocused] = React.useState(false)
  const [isVoiceRecording, setIsVoiceRecording] = React.useState(false)

  const [selectedCategory, setSelectedCategory] = React.useState(0)
  const [isDragOver, setIsDragOver] = React.useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const suggestionCategories: SuggestionCategory[] = [
    {
      name: "Analytics",
      icon: ChartPieIcon,
      suggestions: ["Show me vendor analytics", "Generate performance report", "Display revenue trends"]
    },
    {
      name: "Operations",
      icon: CogIcon,
      suggestions: ["Generate team insights", "Check system status", "Review workflow efficiency"]
    },
    {
      name: "Projects",
      icon: WorkflowIcon,
      suggestions: ["Create a new project", "Show project timeline", "Update project status"]
    },
    {
      name: "Export",
      icon: UploadIcon,
      suggestions: ["Export last week's data", "Download monthly report", "Generate CSV export"]
    }
  ]
  
  const currentSuggestions = suggestionCategories[selectedCategory]?.suggestions || []

  // Scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'Enter') {
        e.preventDefault()
        handleSubmit(e as any)
      } else if (e.key === 'k') {
        e.preventDefault()
        inputRef.current?.focus()
      }
    }
    if (e.key === 'Escape') {
      setMessage('')

      inputRef.current?.blur()
    }
  }, [message])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  const addMessage = useCallback((text: string, isUser: boolean, type?: Message['type']) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      isUser,
      timestamp: new Date(),
      type
    }
    setMessages(prev => [...prev, newMessage])
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim()) {
      addMessage(message, true)
      setIsTyping(true)
      setMessage('')
      
      // Simulate AI response
      setTimeout(() => {
        const responses = [
          "I've analyzed your request and here are the insights...",
          "Based on your data, I recommend the following actions...",
          "Here's what I found in your business operations...",
          "I've generated the report you requested. Here are the key findings..."
        ]
        const randomResponse = responses[Math.floor(Math.random() * responses.length)]
        addMessage(randomResponse, false, 'info')
        setIsTyping(false)
      }, 1500 + Math.random() * 1000)
    }
  }

  const handleVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setIsVoiceRecording(!isVoiceRecording)
      // Voice recognition implementation would go here
      setTimeout(() => setIsVoiceRecording(false), 3000)
    }
  }

  const handleFileUpload = (files: FileList | null) => {
    if (files && files.length > 0) {
      const file = files[0]
      addMessage(`Uploaded: ${file.name} (${(file.size / 1024).toFixed(1)}KB)`, true)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileUpload(e.dataTransfer.files)
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Chat Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="px-6 py-5 border-b border-gray-100 dark:border-gray-800 mb-6"
      >
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              AI Assistant
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Ask anything, get intelligent responses
            </p>
          </div>
          <div className="flex items-center space-x-2 text-xs text-gray-400 dark:text-gray-500">
            <span className="px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded border text-gray-600 dark:text-gray-300">⌘K</span>
            <span className="px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded border text-gray-600 dark:text-gray-300">⌘↵</span>
            <span className="px-2 py-1 bg-gray-50 dark:bg-gray-800 rounded border text-gray-600 dark:text-gray-300">ESC</span>
          </div>
        </div>
      </motion.div>

      {/* Message History */}
      <AnimatePresence>
        {messages.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6 max-h-96 overflow-y-auto bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/30"
          >
            <div className="p-4 space-y-4">
              {messages.map((msg) => (
                <motion.div
                  key={msg.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${msg.isUser ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                    msg.isUser 
                      ? 'bg-blue-500 text-white ml-auto' 
                      : `bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
                          msg.type === 'success' ? 'border-l-4 border-green-500' :
                          msg.type === 'warning' ? 'border-l-4 border-yellow-500' :
                          msg.type === 'error' ? 'border-l-4 border-red-500' :
                          msg.type === 'info' ? 'border-l-4 border-blue-500' : ''
                        }`
                  }`}>
                    <p className="text-sm">{msg.text}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                </motion.div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Chat Container */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`relative bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm rounded-2xl shadow-lg border transition-all duration-500 overflow-hidden ${
          isDragOver 
            ? 'border-blue-500 ring-2 ring-blue-500/30 shadow-blue-500/25 bg-blue-50/30 dark:bg-blue-900/10' 
            : isFocused 
            ? 'ring-2 ring-blue-500/50 shadow-blue-500/25 border-gray-200/50 dark:border-gray-700/50' 
            : 'border-gray-200/50 dark:border-gray-700/50'
        }`}
      >
        {/* Drag Overlay */}
        <AnimatePresence>
          {isDragOver && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-blue-500/10 backdrop-blur-sm z-20 flex items-center justify-center"
            >
              <div className="text-center">
                <div className="text-4xl mb-2">
                  <UploadIcon size={48} className="mx-auto text-blue-600 dark:text-blue-400" />
                </div>
                <p className="text-blue-600 dark:text-blue-400 font-medium">Drop files here to upload</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-teal-500/5 animate-pulse" />
        
        {/* Chat Input Form */}
        <form onSubmit={handleSubmit} className="relative p-4 sm:p-6 lg:p-8">
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            onChange={(e) => handleFileUpload(e.target.files)}
            accept=".pdf,.doc,.docx,.txt,.csv,.xlsx,.png,.jpg,.jpeg"
            multiple
          />
          
          <div className="flex items-center gap-2 sm:gap-4">
            {/* AI Avatar */}
            <motion.div
              animate={{ 
                boxShadow: isTyping ? [
                  "0 0 0 0 rgba(59, 130, 246, 0.7)",
                  "0 0 0 20px rgba(59, 130, 246, 0)",
                  "0 0 0 0 rgba(59, 130, 246, 0)"
                ] : "0 0 0 0 rgba(59, 130, 246, 0)"
              }}
              transition={{
                duration: 1.5,
                repeat: isTyping ? Infinity : 0,
                ease: "easeInOut"
              }}
              className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center"
            >
              <AnimatePresence mode="wait">
                {isTyping ? (
                  <motion.div
                    key="typing"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="flex space-x-1"
                  >
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        animate={{
                          y: [0, -4, 0],
                          opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 0.6,
                          repeat: Infinity,
                          delay: i * 0.1,
                          ease: "easeInOut"
                        }}
                        className="w-1.5 h-1.5 bg-white rounded-full"
                      />
                    ))}
                  </motion.div>
                ) : (
                  <motion.div
                    key="ai-icon"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="w-6 h-6 bg-white/20 rounded-full"
                  />
                )}
              </AnimatePresence>
            </motion.div>

            {/* Input Field */}
            <div className="flex-1 relative">
              <div className="flex items-center">
                <input
                  ref={inputRef}
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  placeholder="Ask me anything about your business..."
                  className="w-full bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-white transition-all duration-200 text-base px-4 py-3 sm:px-6 sm:py-4 pr-20 sm:pr-24"
                  disabled={isTyping}
                />
                
                {/* Input Actions */}
                <div className="absolute right-2 sm:right-4 flex items-center gap-1 sm:gap-2">
                  {/* File Upload Button */}
                  <motion.button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="p-1.5 sm:p-2 text-gray-400 hover:text-blue-500 transition-colors"
                    title="Upload file"
                  >
                    <UploadIcon size={20} className="w-4 h-4 sm:w-5 sm:h-5" />
                  </motion.button>
                  
                  {/* Voice Input Button */}
                  <motion.button
                    type="button"
                    onClick={handleVoiceInput}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={`p-1.5 sm:p-2 transition-colors ${
                      isVoiceRecording
                        ? 'text-red-500 animate-pulse'
                        : 'text-gray-400 hover:text-blue-500'
                    }`}
                    title={isVoiceRecording ? 'Stop recording' : 'Voice input'}
                  >
                    <AudioLinesIcon size={20} className="w-4 h-4 sm:w-5 sm:h-5" />
                  </motion.button>
                  

                </div>
              </div>
              
              {/* Animated Underline */}
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: isFocused ? 1 : 0 }}
                transition={{ duration: 0.3 }}
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 origin-left"
              />
              

            </div>

            {/* Send Button */}
            <motion.button
              type="submit"
              disabled={!message.trim() || isTyping}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-4 py-3 sm:px-6 sm:py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-xl font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
            >
              <div className="flex items-center space-x-2">
                {isTyping ? (
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <ArrowRightIcon size={20} />
                )}
                <span className="hidden sm:inline">Send</span>
              </div>
            </motion.button>
          </div>
        </form>

        {/* Quick Suggestions */}
        <AnimatePresence>
          {!message && !isTyping && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="px-4 sm:px-8 pb-6 sm:pb-8"
            >
              {/* Category Tabs */}
              <div className="flex justify-center mb-4 overflow-x-auto">
                <div className="flex gap-1 p-1 bg-white/20 dark:bg-gray-800/20 rounded-full backdrop-blur-sm">
                  {suggestionCategories.map((category, index) => {
                    const IconComponent = category.icon
                    return (
                      <motion.button
                        key={category.name}
                        onClick={() => setSelectedCategory(index)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-300 whitespace-nowrap ${
                          selectedCategory === index
                            ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <IconComponent size={14} />
                        {category.name}
                      </motion.button>
                    )
                  })}
                </div>
              </div>
              
              {/* Suggestions */}
              <div className="flex flex-wrap gap-2 justify-center">
                {currentSuggestions.map((suggestion, index) => (
                  <motion.button
                    key={suggestion}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setMessage(suggestion)}
                    className="px-3 sm:px-4 py-2 bg-white/40 dark:bg-gray-700/40 backdrop-blur-sm rounded-full text-xs sm:text-sm text-gray-700 dark:text-gray-300 hover:bg-white/60 dark:hover:bg-gray-700/60 transition-all duration-300 border border-white/20 dark:border-gray-600/20"
                  >
                    {suggestion}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Status Indicator */}
        <AnimatePresence>
          {(isTyping || isVoiceRecording) && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="flex items-center justify-center space-x-3 py-3 px-6 bg-gray-50 dark:bg-gray-900 border-t border-gray-100 dark:border-gray-800"
            >
              {isVoiceRecording ? (
                <>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="w-2 h-2 bg-red-500 rounded-full"
                  />
                  <span className="text-sm text-red-600 dark:text-red-400">
                    Recording...
                  </span>
                </>
              ) : (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full"
                  />
                  <span className="text-sm text-blue-600 dark:text-blue-400">
                    AI is thinking...
                  </span>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>


    </div>
  )
}