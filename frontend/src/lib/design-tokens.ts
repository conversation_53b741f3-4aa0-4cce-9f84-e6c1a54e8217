/**
 * Design Tokens System for Lighthouse UI
 * Provides consistent spacing, typography, colors, and elevation values
 */

export const designTokens = {
  // Spacing system using modular scale
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '3rem',    // 48px
    '3xl': '4rem',    // 64px
  },

  // Typography scale with consistent ratios
  typography: {
    // Font sizes
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px

    // Font weights
    weights: {
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },

    // Line heights
    lineHeights: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
  },

  // Semantic color roles
  colors: {
    // Status colors
    success: 'hsl(142 76% 36%)',
    warning: 'hsl(38 92% 50%)',
    error: 'hsl(0 84% 60%)',
    info: 'hsl(221 83% 53%)',

    // Neutral shades
    neutral: {
      50: 'hsl(210 40% 98%)',
      100: 'hsl(210 40% 96%)',
      200: 'hsl(214 32% 91%)',
      300: 'hsl(213 27% 84%)',
      400: 'hsl(215 20% 65%)',
      500: 'hsl(215 16% 47%)',
      600: 'hsl(215 19% 35%)',
      700: 'hsl(215 25% 27%)',
      800: 'hsl(217 33% 17%)',
      900: 'hsl(222 84% 5%)',
    },

    // Brand colors
    primary: {
      50: 'hsl(213 100% 97%)',
      100: 'hsl(214 95% 93%)',
      200: 'hsl(213 97% 87%)',
      300: 'hsl(212 96% 78%)',
      400: 'hsl(213 94% 68%)',
      500: 'hsl(217 91% 60%)',
      600: 'hsl(221 83% 53%)',
      700: 'hsl(224 76% 48%)',
      800: 'hsl(226 71% 40%)',
      900: 'hsl(224 64% 33%)',
    },
  },

  // Elevation system
  elevation: {
    xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  },

  // Border radius
  radius: {
    none: '0',
    sm: '0.125rem',   // 2px
    md: '0.25rem',    // 4px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    full: '9999px',
  },

  // Animation durations and easings
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms',
    },
    easing: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    },
  },

  // Breakpoints for responsive design
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Z-index scale
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800,
  },
} as const;

// Type helpers for strict typing
export type SpacingToken = keyof typeof designTokens.spacing;
export type TypographySize = keyof typeof designTokens.typography;
export type ColorToken = keyof typeof designTokens.colors;
export type ElevationToken = keyof typeof designTokens.elevation;
export type RadiusToken = keyof typeof designTokens.radius;
export type AnimationDuration = keyof typeof designTokens.animation.duration;
export type AnimationEasing = keyof typeof designTokens.animation.easing;

// Utility functions for accessing tokens
export const spacing = (token: SpacingToken) => designTokens.spacing[token];
export const fontSize = (size: TypographySize) => designTokens.typography[size];
export const elevation = (level: ElevationToken) => designTokens.elevation[level];
export const radius = (size: RadiusToken) => designTokens.radius[size];

// CSS custom properties for runtime theming
export const cssVariables = {
  '--spacing-xs': designTokens.spacing.xs,
  '--spacing-sm': designTokens.spacing.sm,
  '--spacing-md': designTokens.spacing.md,
  '--spacing-lg': designTokens.spacing.lg,
  '--spacing-xl': designTokens.spacing.xl,
  '--spacing-2xl': designTokens.spacing['2xl'],
  '--spacing-3xl': designTokens.spacing['3xl'],
  
  '--font-size-xs': designTokens.typography.xs,
  '--font-size-sm': designTokens.typography.sm,
  '--font-size-base': designTokens.typography.base,
  '--font-size-lg': designTokens.typography.lg,
  '--font-size-xl': designTokens.typography.xl,
  '--font-size-2xl': designTokens.typography['2xl'],
  '--font-size-3xl': designTokens.typography['3xl'],
  '--font-size-4xl': designTokens.typography['4xl'],
} as const;