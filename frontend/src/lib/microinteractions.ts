/**
 * Microinteractions Library - Enhanced hover, focus, and transition effects
 */

import { type ClassValue } from 'clsx';
import { cn } from './ui-utils';

// Hover effect presets
export const hoverEffects = {
  // Scale effects
  scaleUp: 'transition-transform duration-200 hover:scale-105 active:scale-95',
  scaleUpSm: 'transition-transform duration-200 hover:scale-102 active:scale-98',
  scaleUpLg: 'transition-transform duration-200 hover:scale-110 active:scale-90',
  
  // Lift effects (translateY + shadow)
  lift: 'transition-all duration-200 hover:-translate-y-1 hover:shadow-md active:translate-y-0',
  liftSm: 'transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm active:translate-y-0',
  liftLg: 'transition-all duration-200 hover:-translate-y-2 hover:shadow-lg active:translate-y-0',
  
  // Slide effects
  slideRight: 'transition-transform duration-200 hover:translate-x-1 active:translate-x-0',
  slideLeft: 'transition-transform duration-200 hover:-translate-x-1 active:translate-x-0',
  
  // Glow effects
  glow: 'transition-all duration-200 hover:shadow-lg hover:shadow-primary/25',
  glowSm: 'transition-all duration-200 hover:shadow-md hover:shadow-primary/20',
  glowLg: 'transition-all duration-200 hover:shadow-xl hover:shadow-primary/30',
  
  // Border effects
  borderGrow: 'transition-all duration-200 border-2 border-transparent hover:border-primary',
  borderFade: 'transition-all duration-200 border border-border hover:border-primary',
  
  // Background effects
  bgSubtle: 'transition-colors duration-200 hover:bg-accent/50',
  bgMuted: 'transition-colors duration-200 hover:bg-muted/80',
  bgPrimary: 'transition-colors duration-200 hover:bg-primary/10',
} as const;

// Focus effect presets (accessibility-first)
export const focusEffects = {
  // Standard focus rings
  ring: 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
  ringLarge: 'focus:outline-none focus:ring-4 focus:ring-primary focus:ring-offset-2',
  ringSubtle: 'focus:outline-none focus:ring-1 focus:ring-primary focus:ring-offset-1',
  
  // Focus-visible (keyboard-only focus)
  visible: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
  visibleLarge: 'focus-visible:outline-none focus-visible:ring-4 focus-visible:ring-primary focus-visible:ring-offset-2',
  
  // Custom focus styles
  border: 'focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary',
  glow: 'focus:outline-none focus:shadow-lg focus:shadow-primary/25',
  
  // Within containers (no offset)
  within: 'focus-within:ring-2 focus-within:ring-primary',
  withinSubtle: 'focus-within:ring-1 focus-within:ring-primary/50',
} as const;

// Loading and state transitions
export const stateTransitions = {
  // Loading states
  loading: 'animate-pulse pointer-events-none opacity-70',
  loadingSubtle: 'animate-pulse opacity-80',
  
  // Disabled states
  disabled: 'opacity-50 pointer-events-none cursor-not-allowed grayscale',
  disabledSubtle: 'opacity-70 cursor-not-allowed',
  
  // Active states
  active: 'bg-primary text-primary-foreground shadow-sm',
  activeSubtle: 'bg-accent text-accent-foreground',
  
  // Success/Error states
  success: 'border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-950 dark:text-green-300',
  error: 'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300',
  warning: 'border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-300',
} as const;

// Animation entrance/exit effects
export const animationEffects = {
  // Fade animations
  fadeIn: 'animate-in fade-in duration-200',
  fadeOut: 'animate-out fade-out duration-150',
  
  // Slide animations
  slideInUp: 'animate-in slide-in-from-bottom-2 duration-200',
  slideInDown: 'animate-in slide-in-from-top-2 duration-200',
  slideInLeft: 'animate-in slide-in-from-right-2 duration-200',
  slideInRight: 'animate-in slide-in-from-left-2 duration-200',
  
  slideOutUp: 'animate-out slide-out-to-top-2 duration-150',
  slideOutDown: 'animate-out slide-out-to-bottom-2 duration-150',
  slideOutLeft: 'animate-out slide-out-to-left-2 duration-150',
  slideOutRight: 'animate-out slide-out-to-right-2 duration-150',
  
  // Scale animations
  scaleIn: 'animate-in zoom-in-95 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-150',
  
  // Bounce effects
  bounceIn: 'animate-in zoom-in-75 duration-300 ease-out',
  bounceOut: 'animate-out zoom-out-75 duration-200 ease-in',
  
  // Rotation effects
  rotateIn: 'animate-in spin-in-90 duration-300',
  rotateOut: 'animate-out spin-out-90 duration-200',
} as const;

// Utility functions for combining effects
export function createInteractiveElement(
  baseClasses: ClassValue,
  options: {
    hover?: keyof typeof hoverEffects;
    focus?: keyof typeof focusEffects;
    state?: keyof typeof stateTransitions;
    animation?: keyof typeof animationEffects;
  } = {}
) {
  const { hover, focus = 'visible', state, animation } = options;
  
  return cn(
    baseClasses,
    hover && hoverEffects[hover],
    focus && focusEffects[focus],
    state && stateTransitions[state],
    animation && animationEffects[animation]
  );
}

// Card interaction presets
export const cardInteractions = {
  subtle: createInteractiveElement('', { hover: 'liftSm', focus: 'visible' }),
  standard: createInteractiveElement('', { hover: 'lift', focus: 'visible' }),
  prominent: createInteractiveElement('', { hover: 'liftLg', focus: 'ring' }),
  
  // With glow effects
  glowSubtle: createInteractiveElement('', { hover: 'glowSm', focus: 'visible' }),
  glowStandard: createInteractiveElement('', { hover: 'glow', focus: 'visible' }),
  glowProminent: createInteractiveElement('', { hover: 'glowLg', focus: 'ring' }),
} as const;

// Button interaction presets
export const buttonInteractions = {
  primary: createInteractiveElement('', { 
    hover: 'scaleUpSm', 
    focus: 'visible',
    animation: 'fadeIn'
  }),
  secondary: createInteractiveElement('', { 
    hover: 'bgSubtle', 
    focus: 'visible',
    animation: 'fadeIn'
  }),
  ghost: createInteractiveElement('', { 
    hover: 'bgMuted', 
    focus: 'visible',
    animation: 'fadeIn'
  }),
  destructive: createInteractiveElement('', { 
    hover: 'scaleUpSm', 
    focus: 'ring',
    animation: 'fadeIn'
  }),
} as const;

// Input interaction presets
export const inputInteractions = {
  standard: createInteractiveElement('', { 
    focus: 'border',
    animation: 'fadeIn'
  }),
  prominent: createInteractiveElement('', { 
    focus: 'ring',
    animation: 'fadeIn'
  }),
  subtle: createInteractiveElement('', { 
    focus: 'ringSubtle',
    animation: 'fadeIn'
  }),
} as const;

// Navigation interaction presets
export const navigationInteractions = {
  sidebar: createInteractiveElement('', { 
    hover: 'slideRight', 
    focus: 'visible',
    animation: 'slideInLeft'
  }),
  tab: createInteractiveElement('', { 
    hover: 'bgSubtle', 
    focus: 'visible',
    animation: 'fadeIn'
  }),
  breadcrumb: createInteractiveElement('', { 
    hover: 'scaleUpSm', 
    focus: 'visible',
    animation: 'fadeIn'
  }),
} as const;

// List item interaction presets
export const listInteractions = {
  subtle: createInteractiveElement('', { 
    hover: 'bgSubtle', 
    focus: 'visible'
  }),
  prominent: createInteractiveElement('', { 
    hover: 'lift', 
    focus: 'ring'
  }),
  borderHighlight: createInteractiveElement('', { 
    hover: 'borderFade', 
    focus: 'visible'
  }),
} as const;

// Utility for creating custom microinteraction combinations
export function createMicrointeraction(config: {
  base?: ClassValue;
  hover?: ClassValue;
  focus?: ClassValue;
  active?: ClassValue;
  disabled?: ClassValue;
  loading?: ClassValue;
}) {
  const { base, hover, focus, active, disabled, loading } = config;
  
  return {
    base: cn(base),
    hover: cn(base, hover),
    focus: cn(base, focus),
    active: cn(base, active),
    disabled: cn(base, disabled),
    loading: cn(base, loading),
    
    // Combined states
    interactive: cn(base, hover, focus),
    all: cn(base, hover, focus, active),
  };
}

// Performance optimizations for animations
export const performanceOptimizations = {
  // Will-change for elements that will be animated
  willChange: 'will-change-transform',
  willChangeAuto: 'will-change-auto',
  
  // GPU acceleration
  gpu: 'transform-gpu',
  
  // Contain for performance isolation
  contain: 'contain-layout contain-style',
  
  // Reduce motion for accessibility
  reduceMotion: 'motion-reduce:transition-none motion-reduce:animate-none',
} as const;

// Accessibility helpers
export const a11yHelpers = {
  // Screen reader only content
  srOnly: 'sr-only',
  
  // Focus management
  focusTrap: 'focus:outline-none focus:ring-2 focus:ring-primary',
  
  // Semantic roles
  button: 'role-button cursor-pointer',
  link: 'role-link cursor-pointer',
  
  // ARIA states
  expanded: (isExpanded: boolean) => ({
    'aria-expanded': isExpanded,
    'data-state': isExpanded ? 'open' : 'closed'
  }),
  
  selected: (isSelected: boolean) => ({
    'aria-selected': isSelected,
    'data-state': isSelected ? 'selected' : 'unselected'
  }),
  
  pressed: (isPressed: boolean) => ({
    'aria-pressed': isPressed,
    'data-state': isPressed ? 'pressed' : 'unpressed'
  }),
} as const;

// Export everything as a single object for easy importing
export const microinteractions = {
  hover: hoverEffects,
  focus: focusEffects,
  state: stateTransitions,
  animation: animationEffects,
  card: cardInteractions,
  button: buttonInteractions,
  input: inputInteractions,
  navigation: navigationInteractions,
  list: listInteractions,
  performance: performanceOptimizations,
  a11y: a11yHelpers,
  create: createMicrointeraction,
  interactive: createInteractiveElement,
} as const;